'use client';

import { useState, useEffect } from 'react';
import { marked } from 'marked';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";

export default function Home() {
  const [query, setQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [result, setResult] = useState('');
  const [responseSource, setResponseSource] = useState('');
  const [entityCount, setEntityCount] = useState(0);

  // 从 localStorage 加载保存的搜索结果
  useEffect(() => {
    // 只在客户端执行
    if (typeof window !== 'undefined') {
      const savedQuery = localStorage.getItem('lastQuery');
      const savedResult = localStorage.getItem('lastResult');
      const savedResponseSource = localStorage.getItem('lastResponseSource');
      const savedEntityCount = localStorage.getItem('lastEntityCount');

      if (savedQuery) setQuery(savedQuery);
      if (savedResult) setResult(savedResult);
      if (savedResponseSource) setResponseSource(savedResponseSource);
      if (savedEntityCount) setEntityCount(parseInt(savedEntityCount, 10) || 0);
    }
  }, []);

  // 配置 marked 库
  useEffect(() => {
    // 设置 marked 选项
    marked.use({
      gfm: true, // 启用 GitHub 风格的 Markdown
      breaks: true, // 启用换行符
      tables: true, // 确保表格支持
      headerIds: false, // 禁用标题ID生成
      mangle: false, // 禁用标题ID混淆
    });
  }, []);

  // 简单的带超时功能的fetch函数
  const fetchWithTimeout = async (url: string, options: RequestInit, timeout = 1200000) => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      console.log(`发送请求到 ${url}`);
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });
      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`请求失败，状态码: ${response.status}, 错误信息: ${errorText}`);
      }

      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  };

  // 清除搜索结果和本地存储
  const clearResults = () => {
    setQuery('');
    setResult('');
    setResponseSource('');
    setEntityCount(0);
    setError('');

    // 清除 localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('lastQuery');
      localStorage.removeItem('lastResult');
      localStorage.removeItem('lastResponseSource');
      localStorage.removeItem('lastEntityCount');
    }

    console.log('搜索结果已清除');
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!query.trim()) {
      setError('请输入搜索内容');
      return;
    }

    setIsLoading(true);
    setError('');
    setResult('');

    try {
      // 添加随机参数，确保不使用缓存
      const timestamp = Date.now();
      const randomParam = Math.random().toString(36).substring(2, 15);

      console.log('发起新的搜索请求，不使用缓存');

      // 使用简单的带超时功能的fetch函数，超时时间为10分钟
      const response = await fetchWithTimeout(`/api/search?_=${timestamp}&r=${randomParam}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        body: JSON.stringify({
          query: query.trim()
        })
      }, 1200000); // 超时时间设为600秒（10分钟）

      // 添加响应处理的性能监控
      console.log('开始处理响应数据...');
      const processStartTime = Date.now();

      const data = await response.json();
      console.log('JSON解析完成，耗时:', (Date.now() - processStartTime) / 1000, '秒');

      if (data.error) {
        setError(data.error);
      } else {
        // 添加调试信息
        console.log(`搜索完成，获取到结果大小:`, JSON.stringify(data).length, '字节');

        if (!data.response) {
          setError('服务器返回了空结果');
          return;
        }

        // 检查响应是否是有效的Markdown表格
        if (!data.response.includes('|') || !data.response.includes('-')) {
          console.warn('响应可能不是有效的Markdown表格格式');
        }

        // 分批处理大型响应，避免UI阻塞
        setTimeout(() => {
          // 设置搜索结果
          setResult(data.response);

          // 保存搜索结果到 localStorage
          if (typeof window !== 'undefined') {
            localStorage.setItem('lastQuery', query.trim());
            localStorage.setItem('lastResult', data.response);
            localStorage.setItem('lastResponseSource', data.responseSource || 'Unknown');
            localStorage.setItem('lastEntityCount', String(data.entityCount || 0));
          }

          console.log('结果渲染完成，总耗时:', (Date.now() - processStartTime) / 1000, '秒');
          console.log('搜索结果已保存到 localStorage');
        }, 100);

        // 设置响应来源和实体数量
        setResponseSource(data.responseSource || 'Unknown');
        setEntityCount(data.entityCount || 0);

        console.log(`响应来源: ${data.responseSource}, 实体数量: ${data.entityCount}`);
      }
    } catch (error: any) {
      // 处理超时错误
      if (error.name === 'AbortError') {
        setError('搜索请求超时，请尝试简化您的搜索内容或稍后再试');
      } else {
        setError(error.message || '搜索过程中出现错误');
      }
      console.error('Search error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex justify-center items-center relative" style={{
      background: `linear-gradient(135deg, rgb(var(--mint-start)), rgb(var(--mint-mid)), rgb(var(--mint-end)))`,
      backgroundSize: '200% 200%',
      animation: 'gradient 15s ease infinite'
    }}>
      {/* 透明蒙版层 - 比背景小一点 */}
      <div className="absolute inset-8 md:inset-12 lg:inset-16 z-0 rounded-3xl overflow-hidden mask-border">
        {/* 主蒙版 */}
        <div className="absolute inset-0 bg-black/15 backdrop-blur-[0.5px] mask-pulse rounded-3xl"></div>

        {/* 动态蒙版效果 */}
        <div className="absolute inset-0 bg-gradient-to-br from-black/5 via-transparent to-black/10 mask-animated rounded-3xl"></div>

        {/* 科技感网格蒙版 */}
        <div className="absolute inset-0 opacity-20 rounded-3xl" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0)`,
          backgroundSize: '30px 30px'
        }}></div>

        {/* 额外的光效蒙版 */}
        <div className="absolute inset-0 bg-gradient-to-t from-transparent via-white/5 to-transparent opacity-30 rounded-3xl"></div>

        {/* 蒙版边框效果 */}
        <div className="absolute inset-0 rounded-3xl border border-white/10 shadow-inner mask-glow"></div>
      </div>

      {/* 背景装饰元素 */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-1">
        <div className="absolute top-10 left-10 w-32 h-32 bg-white opacity-10 rounded-full"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-white opacity-10 rounded-full"></div>
        <div className="absolute top-1/3 right-1/4 w-24 h-24 bg-white opacity-5 rounded-full"></div>
        <div className="absolute bottom-1/4 left-1/3 w-20 h-20 bg-white opacity-5 rounded-full"></div>
      </div>

      {/* 主内容容器 */}
      <Card className="max-w-4xl w-full mx-4 my-8 glass-effect shadow-2xl overflow-hidden z-20 fade-in relative">
        {/* 彩色顶部边框 */}
        <div className="h-1.5 bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-end))]"></div>

        <CardHeader className="text-center py-12">
          <div className="space-y-6">
            <div className="relative">
              <CardTitle className="text-7xl md:text-8xl font-bold bg-gradient-to-r from-[rgb(var(--mint-start))] via-[rgb(var(--mint-mid))] to-[rgb(var(--mint-end))] text-transparent bg-clip-text tracking-tight">
                GooSearch
              </CardTitle>
              <div className="absolute -inset-1 bg-gradient-to-r from-[rgb(var(--mint-start))] via-[rgb(var(--mint-mid))] to-[rgb(var(--mint-end))] rounded-lg blur opacity-20 animate-pulse"></div>
            </div>

            <CardDescription className="text-3xl md:text-4xl font-medium text-[rgb(var(--text-secondary))] max-w-2xl mx-auto leading-relaxed">
              🚀 AI 增强的谷歌搜索引擎
            </CardDescription>

            <div className="flex flex-wrap justify-center gap-3 mt-6">
              <a
                href="/table-demo"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[rgba(var(--mint-end),0.1)] to-[rgba(var(--mint-start),0.1)] text-[rgb(var(--mint-end))] rounded-full text-lg font-medium border border-[rgba(var(--mint-end),0.2)] transition-all duration-300 hover:shadow-lg hover:scale-105 hover:bg-gradient-to-r hover:from-[rgb(var(--mint-end))] hover:to-[rgb(var(--mint-start))] hover:text-white"
              >
                ✨ 表格演示
              </a>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-8 px-8">
          {/* 搜索表单 */}
          <form onSubmit={handleSearch} className="max-w-4xl mx-auto">
            <div className="relative group">
              {/* 搜索框外层光晕效果 */}
              <div className="absolute -inset-1 bg-gradient-to-r from-[rgb(var(--mint-start))] via-[rgb(var(--mint-mid))] to-[rgb(var(--mint-end))] rounded-2xl blur-lg opacity-30 group-hover:opacity-60 transition-all duration-500"></div>

              <div className="relative flex flex-col sm:flex-row gap-0 bg-white rounded-2xl shadow-2xl overflow-hidden border border-[rgba(var(--mint-mid),0.2)]">
                <div className="relative flex-1">
                  <Input
                    type="text"
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    placeholder="🔍 输入您想要搜索的内容..."
                    className="w-full px-8 py-7 border-0 rounded-2xl sm:rounded-r-none focus-visible:ring-0 focus-visible:ring-offset-0 text-2xl placeholder:text-[rgb(var(--text-tertiary))] bg-transparent font-medium text-center placeholder:text-center placeholder:text-2xl"
                    autoFocus
                  />
                  {!isLoading && query && (
                    <button
                      type="button"
                      onClick={() => setQuery('')}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 flex items-center justify-center rounded-full bg-[rgba(var(--mint-start),0.1)] text-[rgb(var(--mint-start))] hover:bg-[rgba(var(--mint-start),0.2)] transition-all duration-200"
                    >
                      ✕
                    </button>
                  )}
                </div>

                <Button
                  type="submit"
                  disabled={isLoading}
                  className="px-10 py-6 bg-gradient-to-r from-[rgb(var(--mint-start))] via-[rgb(var(--mint-mid))] to-[rgb(var(--mint-end))] hover:from-[rgb(var(--mint-mid))] hover:via-[rgb(var(--mint-end))] hover:to-[rgb(var(--mint-start))] text-white font-bold rounded-2xl sm:rounded-l-none transition-all duration-500 shadow-lg hover:shadow-2xl transform hover:scale-105 text-lg relative overflow-hidden group/btn whitespace-nowrap"
                >
                  {/* 按钮内部光效 */}
                  <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover/btn:translate-x-[100%] transition-transform duration-1000"></div>

                  {isLoading ? (
                    <div className="flex items-center space-x-3 relative z-10">
                      <div className="w-6 h-6 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>搜索中...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-3 relative z-10">
                      <span className="text-2xl">🚀</span>
                      <span className="text-2xl">智能搜索</span>
                    </div>
                  )}
                </Button>
              </div>
            </div>

            {/* 搜索提示 */}
            <div className="text-center mt-6">
              <p className="text-xl text-[rgb(var(--text-secondary))] font-medium">
                💡 试试搜索：<span className="text-[rgb(var(--mint-start))] font-semibold cursor-pointer hover:underline" onClick={() => setQuery("人工智能发展趋势")}>"人工智能发展趋势"</span> 或 <span className="text-[rgb(var(--mint-start))] font-semibold cursor-pointer hover:underline" onClick={() => setQuery("最新科技新闻")}>"最新科技新闻"</span>
              </p>
            </div>
          </form>

          <Separator className="my-6 bg-[rgba(var(--mint-mid),0.1)]" />

          {/* 错误提示 */}
          {error && (
            <div className="max-w-2xl mx-auto">
              <div className="p-6 bg-[rgba(var(--accent-1),0.1)] border border-[rgb(var(--accent-1))] text-[rgb(var(--text-primary))] rounded-2xl shadow-lg fade-in">
                <div className="flex items-center justify-center">
                  <div className="flex-shrink-0">
                    <svg className="h-6 w-6 text-[rgb(var(--accent-1))]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-base font-medium text-center">{error}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 加载状态 */}
          {isLoading && (
            <div className="text-center py-10 fade-in">
              <p className="text-[rgb(var(--mint-start))] font-medium text-xl mb-4">正在搜索并分析结果</p>
              <p className="text-[rgb(var(--text-secondary))] text-base max-w-md mx-auto mb-4">
                我们正在爬取和分析相关网页，这可能需要一些时间（通常1-3分钟），取决于查询的复杂性
              </p>
              <div className="flex flex-col items-center justify-center space-y-6">
                <div className="w-16 h-16 border-4 border-[rgba(var(--mint-start),0.2)] border-t-[rgb(var(--mint-start))] rounded-full animate-spin mx-auto shadow-md"></div>
                <div className="text-[rgb(var(--mint-mid))] font-medium text-lg animate-pulse">
                  请耐心等待，系统正在处理中...
                </div>
              </div>
            </div>
          )}

          {/* 搜索结果 */}
          {result && (
            <Card className="mt-8 card-hover fade-in border border-[rgba(var(--mint-mid),0.2)] shadow-xl overflow-hidden">
              <div className="bg-gradient-to-r from-[rgb(var(--mint-start))] to-[rgb(var(--mint-end))] px-6 py-4 text-center relative">
                <h2 className="text-xl font-bold text-white">搜索结果</h2>
                <div className="flex justify-center mt-2 space-x-4">
                  <span className="inline-flex items-center px-2 py-1 bg-white/20 rounded-full text-xs font-medium text-white">
                    数据来源: {responseSource === 'AI' ? 'AI生成' : responseSource === 'Cache' ? '缓存' : '自动生成'}
                  </span>
                  <span className="inline-flex items-center px-2 py-1 bg-white/20 rounded-full text-xs font-medium text-white">
                    实体数量: {entityCount}
                  </span>
                </div>

                {/* 清除按钮 */}
                <button
                  onClick={clearResults}
                  className="absolute right-4 top-4 text-white/70 hover:text-white transition-colors"
                  title="清除结果"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>

              <CardContent className="p-6 md:p-8 center-all-content">
                {/* 渲染Markdown内容 */}
                <div className={`table-container search-result-content markdown-content ${result.includes('|') && result.includes('-') ? 'table-success' : ''}`}>
                  <div
                    dangerouslySetInnerHTML={{ __html: marked.parse(result) }}
                    className="prose max-w-none prose-headings:text-center prose-headings:text-[rgb(var(--text-primary))] prose-a:text-[rgb(var(--mint-start))] prose-strong:text-[rgb(var(--mint-start))] enhanced-table text-center-force"
                  />
                </div>

                {/* 添加表格渲染状态提示 */}
                <div className="mt-8 text-center">
                  {result.includes('|') && result.includes('-') ? (
                    <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[rgba(var(--mint-start),0.1)] to-[rgba(var(--mint-mid),0.1)] text-[rgb(var(--mint-start))] rounded-full text-sm font-semibold border-2 border-[rgba(var(--mint-start),0.2)] shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 animate-pulse" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      ✨ 表格已成功渲染
                    </div>
                  ) : (
                    <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[rgba(var(--accent-2),0.1)] to-[rgba(var(--accent-1),0.1)] text-[rgb(var(--accent-2))] rounded-full text-sm font-semibold border-2 border-[rgba(var(--accent-2),0.2)] shadow-lg">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                      </svg>
                      📄 文本格式内容
                    </div>
                  )}
                </div>

                {/* 始终显示原始结果，确保用户能看到内容 */}
                <div className="mt-8 p-6 bg-[rgba(var(--accent-2),0.05)] border border-[rgba(var(--accent-2),0.2)] rounded-xl">
                  <h3 className="text-[rgb(var(--text-primary))] font-semibold mb-4 text-center">原始响应</h3>
                  <div className="bg-white p-5 rounded-lg border border-[rgba(var(--mint-mid),0.2)] overflow-auto max-h-96">
                    <pre className="whitespace-pre-wrap text-sm text-[rgb(var(--text-secondary))] text-center">
                      {result}
                    </pre>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 空状态 */}
          {!isLoading && !error && !result && (
            <div className="text-center py-20 fade-in">
              <div className="max-w-2xl mx-auto space-y-8">
                {/* 主标题 */}
                <div className="relative">
                  <h3 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-[rgb(var(--mint-start))] via-[rgb(var(--mint-mid))] to-[rgb(var(--mint-end))] bg-clip-text text-transparent leading-tight">
                    开始您的智能搜索之旅
                  </h3>
                  <div className="absolute -inset-2 bg-gradient-to-r from-[rgb(var(--mint-start))] via-[rgb(var(--mint-mid))] to-[rgb(var(--mint-end))] rounded-lg blur-2xl opacity-10 animate-pulse"></div>
                </div>

                {/* 描述文字 */}
                <p className="text-[rgb(var(--text-secondary))] text-xl leading-relaxed max-w-xl mx-auto">
                  🌟 体验 AI 驱动的智能搜索，获得更精准、更深入的搜索结果分析
                </p>

                {/* 功能特点 */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
                  <div className="p-4 bg-gradient-to-br from-[rgba(var(--mint-start),0.05)] to-[rgba(var(--mint-mid),0.05)] rounded-2xl border border-[rgba(var(--mint-start),0.1)] hover:shadow-lg transition-all duration-300 hover:scale-105 text-center">
                    <h4 className="font-bold text-[rgb(var(--text-primary))] mb-2 text-sm">🎯精准搜索</h4>
                    <p className="text-xs text-[rgb(var(--text-secondary))]">AI 理解您的搜索意图，提供最相关的结果</p>
                  </div>

                  <div className="p-4 bg-gradient-to-br from-[rgba(var(--mint-mid),0.05)] to-[rgba(var(--mint-end),0.05)] rounded-2xl border border-[rgba(var(--mint-mid),0.1)] hover:shadow-lg transition-all duration-300 hover:scale-105 text-center">
                    <h4 className="font-bold text-[rgb(var(--text-primary))] mb-2 text-sm">⚡智能分析</h4>
                    <p className="text-xs text-[rgb(var(--text-secondary))]">自动提取关键信息，生成结构化数据</p>
                  </div>

                  <div className="p-4 bg-gradient-to-br from-[rgba(var(--mint-end),0.05)] to-[rgba(var(--mint-start),0.05)] rounded-2xl border border-[rgba(var(--mint-end),0.1)] hover:shadow-lg transition-all duration-300 hover:scale-105 text-center">
                    <h4 className="font-bold text-[rgb(var(--text-primary))] mb-2 text-sm">📊表格展示</h4>
                    <p className="text-xs text-[rgb(var(--text-secondary))]">美观的表格格式，数据一目了然</p>
                  </div>
                </div>

                {/* 示例搜索 */}
                <div className="mt-16">
                  <p className="text-[rgb(var(--text-secondary))] mb-8 font-medium text-sm">🔥 热门搜索示例：</p>
                  <div className="flex flex-wrap justify-center gap-8">
                    {[
                      "人工智能发展趋势",
                      "最新科技新闻",
                      "区块链技术应用",
                      "量子计算进展",
                      "新能源汽车市场"
                    ].map((example, index) => (
                      <button
                        key={index}
                        onClick={() => setQuery(example)}
                        className="group relative px-6 py-2 bg-white/90 backdrop-blur-sm text-[rgb(var(--mint-start))] rounded-full text-sm font-semibold border-2 border-[rgba(var(--mint-start),0.3)] transition-all duration-300 hover:shadow-xl hover:scale-110 hover:border-[rgb(var(--mint-start))] hover:bg-gradient-to-r hover:from-[rgb(var(--mint-start))] hover:to-[rgb(var(--mint-mid))] hover:text-white overflow-hidden example-button example-border"
                        style={{
                          animationDelay: `${index * 0.2}s`
                        }}
                      >
                        {/* 背景光效 */}
                        <div className="absolute inset-0 bg-gradient-to-r from-[rgba(var(--mint-start),0.1)] via-[rgba(var(--mint-mid),0.05)] to-[rgba(var(--mint-end),0.1)] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                        {/* 闪光效果 */}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>

                        {/* 文字内容 */}
                        <span className="relative z-10">{example}</span>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>

        <CardFooter className="border-t border-[rgba(var(--mint-mid),0.1)] bg-[rgba(var(--mint-start),0.03)] p-6 justify-center">
          <p className="text-[rgb(var(--text-secondary))] text-xs">
            使用 AI 增强的搜索结果分析 | Powered by Next.js
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}
